import request from "./requests.js"
// import request from "../config/request"
import axios from "axios";



export const uploadToOSS = async (file, onProgress) => {
	const formData = new FormData();
	formData.append('file', file);
	let token = uni.getStorageSync('welcome')
	console.log(token);
	return axios.post('/oss/api/resource/oss/upload', formData, {
			headers: {
					'Content-Type': 'multipart/form-data',
					'Authorization': `${token}` // 根据实际鉴权方式调整
			},
			onUploadProgress: (progressEvent) => {
					if (onProgress && progressEvent.total) {
							const percent = Math.round((progressEvent.loaded * 100) / progressEvent.total);
							onProgress(percent);
					}
			}
	});
}

export function welcome(){
	return request.get("/api/funFunEsthetics/welcome")
}
// 调用AI
export function callAi(params){
	return request.post("/api/funFunEsthetics/invoke/AI",params)
}

// 轮训调用AI
export function callAiPolling(params){
	return request.post("/api/funFunEsthetics/getAIStatus",params)
}