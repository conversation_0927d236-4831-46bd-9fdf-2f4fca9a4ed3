<template>
    <view class="min-h-screen bg-[#fefcf8]">
        <!-- 头部 -->
        <view class="fixed top-0 left-0 w-full h-10 header flex items-center p-2 bg-[#FEFCF8]" style="z-index: 1;">
            <uni-icons class="absolute left-0" @click="goBack" type="left" size="24" color="#666"></uni-icons>
            <text class="text-center flex-1 text-lg font-medium">专属推荐</text>
        </view>
        <view class="max-w-[800rpx] mx-auto py-3">
            <view v-for="(item, idx) in items" :key="idx" class=" bg-white shadow-sm m-4 overflow-hidden">
                <img :src="item.img" :alt="item.imgAlt" class=" w-full h-[220rpx] object-cover" />
                <view class="pb-4">
                    <view class="flex items-center justify-between h-12 bg-[#F6F6F6] px-2">
                        <view class="flex items-center">
                            <text class="text-[#333] font-medium" style="font-size: 30rpx;">{{ item.title }}</text>
                            <text class="text-[#999] ml-2" style="font-size: 26rpx;">{{ item.price }}</text>
                        </view>
                        <button @click="handleGet(true)" class="ml-3 bg-[#ff7e7e] text-white px-3 mr-0 py-1 font-medium"
                            style="font-size: 24rpx;line-height: 30rpx;">
                            领取专属福利
                        </button>
                    </view>
                    <view class="px-3 mt-3 text-[#999]  flex items-center" style="font-size: 26rpx;">
                        美学增量
                        <text class="ml-1">
                            <text class="text-[#ff9900]" style="font-size: 26rpx;">★</text>
                            <text class="text-[#ff9900]" style="font-size: 26rpx;">★</text>
                            <text class="text-[#ff9900]" style="font-size: 26rpx;">★</text>
                        </text>
                    </view>
                    <text class="px-3  mt-1 text-[#333] font-medium block" style="font-size: 30rpx;">{{ item.subtitle }}</text>
                    <text class="px-3  mt-1 text-[#666] leading-[40rpx] block" style="font-size: 26rpx;">{{ item.desc }}</text>
                </view>
            </view>
        </view>
    </view>
    <view v-if="!!getVisible" class="popup-overlay">
        <view class="popupBox rounded-[4px] ">
            <text>领取成功</text>
            <div class="w-[200px]">
                <div class="bg-[#FFBFC3] rounded-[4px] px-10 pt-3 pb-6 relative">
                    <!-- Top dots -->
                    <div class="flex space-x-1 absolute left-1 top-1">
                        <span class="inline-block w-1 h-1 bg-white opacity-40 rounded-full"></span>
                        <span class="inline-block w-1 h-1 bg-white opacity-40 rounded-full"></span>
                        <span class="inline-block w-1 h-1 bg-white opacity-40 rounded-full"></span>
                        <span class="inline-block w-1 h-1 bg-white opacity-40 rounded-full"></span>
                        <span class="inline-block w-1 h-1 bg-white opacity-40 rounded-full"></span>
                    </div>
                    <!-- Main content -->
                    <div class="flex flex-col items-center justify-center mt-2">
                        <div class="flex items-end">
                            <span class="text-white text-[36px] leading-none font-normal"
                                style="font-family: 'Roboto', sans-serif;">200</span>
                            <span class="text-white text-[36px] leading-none font-normal ml-1"
                                style="font-family: 'Roboto', sans-serif;">元</span>
                        </div>
                        <div class="mt-2 text-white text-[14px] font-normal opacity-90"
                            style="font-family: 'Roboto', sans-serif;">
                            免门槛到店使用
                        </div>
                    </div>
                </div>
            </div>
            <uni-icons class="absolute right-0 top-[-30px]" @click="handleGet(false)" type="closeempty" size="24"
                color="#fff"></uni-icons>
        </view>
    </view>
</template>

<script setup>
import { ref } from 'vue';

const items = ref([
    {
        img: "/static/imgs/pro1.png",
        imgAlt: "水晶胶质感的脸部特写，带有水珠和清新蓝色背景，突出医美方案的高端感",
        title: "明星医师方案",
        price: "5500～7500元",
        subtitle: "玻尿酸+肉毒素+光子嫩肤",
        desc: "玻尿酸填充太阳穴/使面部线条更流畅，脸部更饱满，避免脸部轮廓凹凸不平。肉毒素淡化皱纹细纹，使脸部更幼态。光子嫩肤修复肌肤屏障，焕发肌肤新生。"
    },
    {
        img: "/static/imgs/pro2.png",
        imgAlt: "年轻女性的脸部特写，皮肤光滑，背景为梦幻蓝紫色灯光，突出医美效果",
        title: "黄金标准方案",
        price: "3500～5500元",
        subtitle: "玻尿酸+肉毒素",
        desc: "玻尿酸填充太阳穴/使面部线条更流畅，脸部更饱满，避免脸部轮廓凹凸不平。肉毒素淡化皱纹细纹，使脸部更幼态。"
    },
    {
        img: "/static/imgs/pro3.png",
        imgAlt: "侧脸女性特写，皮肤细腻，背景为柔和暖色调，突出经典医美方案",
        title: "经典甄选方案",
        price: "3500～5500元",
        subtitle: "玻尿酸",
        desc: "玻尿酸填充太阳穴/使面部线条更流畅，脸部更饱满，避免脸部轮廓凹凸不平。效果更温和。"
    }
]);

// 返回上一页
const goBack = () => {
    uni.navigateBack();
};

const getVisible = ref(false)
const handleGet = (flag) => {
    getVisible.value = flag
}

</script>

<style scoped lang="scss">
/* 全局样式 */
page {
    background-color: #fefcf8;
    font-family: 'PingFang SC', 'Helvetica Neue', Arial, 'Microsoft YaHei', sans-serif;
}

.popup-overlay {
    position: fixed;
    top: 0;
    left: 0;
    z-index: 1;
    width: 100%;
    height: 100%;
    background-color: rgba(0, 0, 0, 0.5);
    display: flex;
    justify-content: center;
    align-items: flex-end;
}

.popupBox {
    position: fixed;
    top: 0;
    left: 0;
    z-index: 1;
    right: 0;
    bottom: 0;
    margin: auto;
    width: max-content;
    height: max-content;
    padding: 40rpx;
    background: #fff;
    text-align: center;
}
</style>