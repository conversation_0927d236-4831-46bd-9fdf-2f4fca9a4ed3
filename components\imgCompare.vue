<template>
    <view class="comparison-container" ref="container" :style="{ height: height + 'px' }" @touchmove="handleInteraction">
        <view class="container--full">
            <view class="slider--comparison">
                <!-- Before 图片区域 -->
                <view ref="beforeWrapper" class="before-wrapper" :style="beforeTransform">
                    <view ref="modalImageBefore" class="before-image modal-image"
                        :style="[beforeImageStyle, beforeImageTransform]">
                        前
                    </view>
                </view>

                <!-- After 图片区域 -->
                <view ref="afterWrapper" class="after-wrapper" :style="afterTransform">
                    <view class="after-image">
                        <view ref="modalImageAfter" class="after-image-inner modal-image"
                            :style="[afterImageStyle, afterImageTransform]">
                            后
                        </view>
                    </view>
                </view>
            </view>
        </view>
    </view>
</template>

<script setup>
import { ref, computed, onMounted, onBeforeUnmount } from 'vue'


const props = defineProps({
    before: {
        type: String,
        default: '/static/images/faceimgs/面部/6_00_bf.jpg'
    },
    after: {
        type: String,
        default: '/static/images/faceimgs/面部/6_00_af.jpg'
    },
    height: {
        type: Number,
        default: 400
    }
})

// DOM 引用
const container = ref(null)
const beforeWrapper = ref(null)
const modalImageBefore = ref(null)
const afterWrapper = ref(null)
const modalImageAfter = ref(null)

// 响应式状态
const percentage = ref(50)
const timeoutId = ref(null)
const containerRect = ref({ left: 0, width: 0 })

// 配置常量
const THROTTLE_DELAY = 0
const TRANSFORM_TEMPLATE = (value) =>
    `transform: translate3d(${value.toFixed(0)}%, 0, 0)`

// 计算属性生成transform样式
const transforms = computed(() => ({
    before: percentage.value - 100,
    beforeImage: 100 - percentage.value,
    after: percentage.value,
    afterImage: -percentage.value
}))

// 动态计算背景图样式
const beforeImageStyle = computed(() => ({
    backgroundImage: `url(${props.before})`
}))

const afterImageStyle = computed(() => ({
    backgroundImage: `url(${props.after})`
}))

const beforeTransform = computed(() => TRANSFORM_TEMPLATE(transforms.value.before))
const beforeImageTransform = computed(() => TRANSFORM_TEMPLATE(transforms.value.beforeImage))
const afterTransform = computed(() => TRANSFORM_TEMPLATE(transforms.value.after))
const afterImageTransform = computed(() => TRANSFORM_TEMPLATE(transforms.value.afterImage))

// 优化后的节流函数
const throttle = (fn, delay) => {
    let lastTime = 0
    return (...args) => {
        const now = Date.now()
        if (now - lastTime >= delay) {
            fn(...args)
            lastTime = now
        }
    }
}

// 百分比计算
const getPercentage = (event) => {
    if (!containerRect.value.width) return 50
    const touch = event.touches[0]
    const clientX = touch.clientX
    const raw = ((clientX - containerRect.value.left) / containerRect.value.width) * 100
    return Math.min(Math.max(raw, 0), 100) // 限制在0-100之间
}

// 事件处理
const handleInteraction = (event) => {
    event.preventDefault()
    const newPercentage = getPercentage(event)
    updatePercentage(newPercentage)
}

// 更新逻辑
const updatePercentage = throttle((value) => {
    percentage.value = value
    resetTimer()
}, THROTTLE_DELAY)

// 自动重置定时器
const resetTimer = () => {
    if (timeoutId.value) {
        clearTimeout(timeoutId.value)
        timeoutId.value = null
    }
    timeoutId.value = setTimeout(() => {
        percentage.value = 50
    }, 1000)
}

// 获取容器位置信息
const updateContainerRect = () => {
    return new Promise((resolve) => {
        uni.createSelectorQuery()
            .in(this)
            .select('.comparison-container')
            .boundingClientRect(data => {
                if (data) {
                    containerRect.value = {
                        left: data.left,
                        width: data.width
                    }
                }
                resolve()
            })
            .exec()
    })
}

// 生命周期
onMounted(async () => {
    await updateContainerRect()
    window.addEventListener('resize', updateContainerRect)
})

onBeforeUnmount(() => {
    window.removeEventListener('resize', updateContainerRect)
    resetTimer()
})
</script>

<style scoped>
.comparison-container {
    position: relative;
    width: 100%;
    height: 400px;
    touch-action: none;
}

.ff-industry__hero-right {
    grid-column-gap: 1rem;
    grid-row-gap: 1rem;
    cursor: ew-resize;
    background-color: #f5f6fb;
    border: none;
    flex: 1;
    justify-content: center;
    align-items: flex-start;
    height: 60vh;
    max-height: 70vh;
    display: flex;
    overflow: hidden;
}

.container--full {
    cursor: ew-resize;
    width: 100%;
    height: 100%;
    position: relative;
    overflow: hidden
}

.slider--comparison {
    height: 100%;
    position: absolute;
    inset: 0%;
    overflow: hidden
}

.before-wrapper {
    justify-content: center;
    align-items: center;
    position: absolute;
    z-index: 3;
    inset: 0%;
    overflow: hidden;
    will-change: transform;
    transform-style: preserve-3d;
    border-right: 1rpx solid #999;
}

.after-wrapper {
    position: absolute;
    z-index: 2;
    inset: 0%;
    overflow: visible;
    will-change: transform;
    transform-style: preserve-3d
}

.modal-image {
    will-change: transform;
    transform-style: preserve-3d;
    width: 100%;
    height: 100%;
    background-size: cover;
    background-position: center;
    display: flex;
    justify-content: center;
    align-items: flex-end;
    font-size: 18px;
    font-weight: bold;
    color: white;
    text-shadow: 0 1px 3px rgba(0, 0, 0, 0.5);
}

.before-image {
    background-position: 50%;
    background-repeat: no-repeat;
    background-size: cover;
    justify-content: center;
    align-items: end;
    display: flex;
    position: absolute;
    inset: 0%
}

.after-image-inner {
    background-position: 50%;
    background-repeat: no-repeat;
    background-size: cover;
    justify-content: center;
    align-items: end;
    display: flex;
    position: absolute;
    inset: 0%;
    overflow: visible
}
</style>
